import React, { useState, useEffect } from 'react';
import { format, differenceInYears, addDays } from 'date-fns';
import { useContactsQuery } from '../hooks/useContacts';
import {
  useScheduledMessagesQuery,
  useScheduleMessage,
  useProcessBirthdayMessages,
  useSettingsQuery,
  useUpdateSettings,
  useLandmarkBirthday
} from '../hooks/useMessaging';

const AutomatedMessagingSystem: React.FC = () => {
  const { data: contacts = [] } = useContactsQuery();
  const { data: scheduledMessages = [], isLoading: messagesLoading } = useScheduledMessagesQuery();
  const { data: settings } = useSettingsQuery();
  const scheduleMessage = useScheduleMessage();
  const processBirthdayMessages = useProcessBirthdayMessages();
  const updateSettings = useUpdateSettings();

  const [isProcessing, setIsProcessing] = useState(false);
  const [showScheduler, setShowScheduler] = useState(false);
  const [selectedContact, setSelectedContact] = useState<any>(null);

  // Auto-process birthday messages daily
  useEffect(() => {
    const processDaily = async () => {
      if (settings?.birthdayMessages.enabled) {
        try {
          await processBirthdayMessages.mutateAsync(contacts);
        } catch (error) {
          console.error('Failed to process birthday messages:', error);
        }
      }
    };

    // Check if we should process messages (simulate daily check)
    const lastProcessed = localStorage.getItem('lastBirthdayProcessed');
    const today = format(new Date(), 'yyyy-MM-dd');

    if (lastProcessed !== today && contacts.length > 0) {
      processDaily();
      localStorage.setItem('lastBirthdayProcessed', today);
    }
  }, [contacts, settings, processBirthdayMessages]);

  const getTodaysBirthdays = () => {
    const today = new Date();
    const todayString = format(today, 'MM-dd');

    return contacts.filter(contact => {
      const contactBirthday = format(new Date(contact.birthday), 'MM-dd');
      return contactBirthday === todayString;
    }).map(contact => {
      const age = differenceInYears(today, new Date(contact.birthday));
      const { isLandmark, landmark } = useLandmarkBirthday(age);

      return {
        ...contact,
        age,
        isLandmark,
        landmark,
      };
    });
  };

  const getUpcomingBirthdays = (days: number = 7) => {
    const today = new Date();
    const upcoming = [];

    for (let i = 1; i <= days; i++) {
      const checkDate = addDays(today, i);
      const checkString = format(checkDate, 'MM-dd');

      const birthdaysOnDate = contacts.filter(contact => {
        const contactBirthday = format(new Date(contact.birthday), 'MM-dd');
        return contactBirthday === checkString;
      }).map(contact => {
        const age = differenceInYears(checkDate, new Date(contact.birthday));
        const { isLandmark, landmark } = useLandmarkBirthday(age);

        return {
          ...contact,
          age,
          isLandmark,
          landmark,
          daysUntil: i,
          date: checkDate,
        };
      });

      upcoming.push(...birthdaysOnDate);
    }

    return upcoming;
  };

  const handleManualProcess = async () => {
    setIsProcessing(true);
    try {
      await processBirthdayMessages.mutateAsync(contacts);
      alert('Birthday messages processed successfully!');
    } catch (error) {
      alert('Failed to process birthday messages');
      console.error(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSettingsUpdate = async (newSettings: any) => {
    try {
      await updateSettings.mutateAsync({
        birthdayMessages: {
          ...settings?.birthdayMessages,
          ...newSettings,
        },
      });
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  };

  const getMessageStatus = (contactId: string, type: 'birthday' | 'landmark') => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const message = scheduledMessages.find(msg =>
      msg.contactId === contactId &&
      msg.type === type &&
      format(new Date(msg.scheduledFor), 'yyyy-MM-dd') === today
    );

    return message?.status || 'none';
  };

  const todaysBirthdays = getTodaysBirthdays();
  const upcomingBirthdays = getUpcomingBirthdays();
  const pendingMessages = scheduledMessages.filter(msg => msg.status === 'pending').length;
  const sentToday = scheduledMessages.filter(msg =>
    msg.status === 'sent' &&
    format(new Date(msg.sentAt || ''), 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
  ).length;

  if (!settings) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* System Status */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Automated Birthday Messaging</h3>
            <p className="text-sm text-gray-600">Automatic birthday messages sent at scheduled times</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 text-xs rounded-full ${settings.birthdayMessages?.enabled
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800'
              }`}>
              {settings.birthdayMessages?.enabled ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-xl">🎂</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-600">Today's Birthdays</p>
                <p className="text-2xl font-bold text-blue-900">{todaysBirthdays.length}</p>
              </div>
            </div>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-xl">📅</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-600">Next 7 Days</p>
                <p className="text-2xl font-bold text-green-900">{upcomingBirthdays.length}</p>
              </div>
            </div>
          </div>

          <div className="p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <span className="text-xl">⏳</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-yellow-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-900">{pendingMessages}</p>
              </div>
            </div>
          </div>

          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-xl">✅</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-600">Sent Today</p>
                <p className="text-2xl font-bold text-purple-900">{sentToday}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Send Time
            </label>
            <input
              type="time"
              value={settings.birthdayMessages.defaultTime}
              onChange={(e) => handleSettingsUpdate({ defaultTime: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Timezone
            </label>
            <select
              value={settings.birthdayMessages.timezone}
              onChange={(e) => handleSettingsUpdate({ timezone: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
            </select>
          </div>
        </div>

        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg mb-6">
          <div>
            <h4 className="font-medium text-gray-900">Enable Automated Messages</h4>
            <p className="text-sm text-gray-600">Automatically send birthday messages at scheduled times</p>
          </div>
          <button
            onClick={() => handleSettingsUpdate({
              enabled: !settings.birthdayMessages.enabled
            })}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${settings.birthdayMessages.enabled ? 'bg-purple-600' : 'bg-gray-200'
              }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${settings.birthdayMessages.enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
            />
          </button>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleManualProcess}
            disabled={isProcessing}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
          >
            {isProcessing ? 'Processing...' : 'Process Now'}
          </button>
          <button
            onClick={() => setShowScheduler(!showScheduler)}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
          >
            {showScheduler ? 'Hide Scheduler' : 'Show Scheduler'}
          </button>
        </div>
      </div>

      {/* Today's Birthdays */}
      {todaysBirthdays.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="font-medium text-gray-900 mb-4">Today's Birthdays 🎉</h4>
          <div className="space-y-3">
            {todaysBirthdays.map(birthday => {
              const messageStatus = getMessageStatus(birthday.id, birthday.isLandmark ? 'landmark' : 'birthday');

              return (
                <div
                  key={birthday.id}
                  className={`p-3 rounded-lg border-l-4 ${birthday.isLandmark
                      ? 'border-yellow-400 bg-yellow-50'
                      : 'border-purple-400 bg-purple-50'
                    }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{birthday.name}</p>
                      <p className="text-sm text-gray-600">
                        Turning {birthday.age}
                        {birthday.isLandmark && birthday.landmark && (
                          <span className="ml-2 px-2 py-1 text-xs bg-yellow-200 text-yellow-800 rounded-full">
                            🎊 {birthday.landmark.name}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`px-2 py-1 text-xs rounded-full ${messageStatus === 'sent' ? 'bg-green-100 text-green-800' :
                          messageStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            messageStatus === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-600'
                        }`}>
                        {messageStatus === 'sent' ? '✅ Sent' :
                          messageStatus === 'pending' ? '⏳ Pending' :
                            messageStatus === 'failed' ? '❌ Failed' :
                              '⚪ Not Scheduled'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Upcoming Birthdays */}
      {upcomingBirthdays.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h4 className="font-medium text-gray-900 mb-4">Upcoming Birthdays (Next 7 Days)</h4>
          <div className="space-y-3">
            {upcomingBirthdays.map(birthday => (
              <div
                key={`${birthday.id}-${birthday.daysUntil}`}
                className="p-3 rounded-lg border border-gray-200 hover:border-purple-300 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">{birthday.name}</p>
                    <p className="text-sm text-gray-600">
                      {format(birthday.date, 'EEEE, MMMM do')} •
                      {birthday.daysUntil === 1 ? ' Tomorrow' : ` In ${birthday.daysUntil} days`} •
                      Turning {birthday.age}
                      {birthday.isLandmark && birthday.landmark && (
                        <span className="ml-2 px-2 py-1 text-xs bg-yellow-200 text-yellow-800 rounded-full">
                          🎊 {birthday.landmark.name}
                        </span>
                      )}
                    </p>
                  </div>
                  <div className="text-sm text-gray-500">
                    {birthday.category}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomatedMessagingSystem;
