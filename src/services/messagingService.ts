import type {
  MessageTemplate,
  ScheduledMessage,
  WeeklyAdminNotification,
  NotificationSettings,
  MessageHistory,
  MessagingStats,
  LandmarkBirthday
} from '../types/messaging';
import type { Contact } from '../types';
import { persistenceService } from './persistenceService';
import { format, addDays, startOfWeek, endOfWeek, differenceInYears, parseISO } from 'date-fns';

// Landmark birthday definitions
export const LANDMARK_BIRTHDAYS: LandmarkBirthday[] = [
  { age: 18, name: 'Coming of Age', description: 'Legal adulthood milestone', isSpecial: true },
  { age: 21, name: 'Legal Drinking Age', description: 'Full legal rights in most countries', isSpecial: true },
  { age: 30, name: 'Thirty and Thriving', description: 'Entering a new decade of life', isSpecial: false },
  { age: 40, name: '<PERSON> Begins at Forty', description: 'Wisdom and experience milestone', isSpecial: false },
  { age: 50, name: 'Golden Jubilee', description: 'Half a century of life', isSpecial: true },
  { age: 60, name: 'Diamond Years', description: 'Entering the golden years', isSpecial: false },
  { age: 65, name: 'Retirement Milestone', description: 'Traditional retirement age', isSpecial: false },
  { age: 70, name: 'Platinum Years', description: 'Seven decades of wisdom', isSpecial: false },
  { age: 75, name: 'Diamond Anniversary', description: 'Three quarters of a century', isSpecial: true },
  { age: 80, name: 'Octogenarian', description: 'Eight decades of life', isSpecial: false },
  { age: 90, name: 'Nonagenarian', description: 'Nine decades of wisdom', isSpecial: true },
  { age: 100, name: 'Centenarian', description: 'A full century of life!', isSpecial: true },
];

class MessagingService {
  private currentUserId: string | null = null;
  private settings: NotificationSettings = {
    id: 'default_settings',
    userId: 'current_user',
    weeklyAdminNotifications: {
      enabled: true,
      dayOfWeek: 1, // Monday
      time: '09:00',
      email: '<EMAIL>',
    },
    birthdayMessages: {
      enabled: true,
      defaultTime: '06:00',
      timezone: 'UTC',
    },
    landmarkBirthdays: {
      enabled: true,
      customMessage: true,
    },
  };

  constructor() {
    this.initializeDefaults();
  }

  // Set current user for database operations
  setCurrentUser(userId: string) {
    this.currentUserId = userId;
    persistenceService.setCurrentUser(userId);
  }

  private async initializeDefaults() {
    try {
      await persistenceService.initializeDefaults();
      await this.seedDefaultTemplatesIfNeeded();
    } catch (error) {
      console.error('Failed to initialize messaging service:', error);
    }
  }

  private async seedDefaultTemplatesIfNeeded() {
    // Check if default templates already exist
    const existingTemplates = persistenceService.getAllGlobal<MessageTemplate>('default_templates');

    if (existingTemplates.length === 0) {
      // Create default templates
      const defaultTemplates = this.getDefaultTemplateData();

      for (const template of defaultTemplates) {
        await persistenceService.createGlobal<MessageTemplate>('default_templates', template);
      }

      console.log(`✅ Seeded ${defaultTemplates.length} default templates`);
    }
  }

  private getDefaultTemplateData(): Omit<MessageTemplate, 'id' | 'createdAt' | 'updatedAt'>[] {
    return [
      {
        name: 'Standard Birthday',
        type: 'birthday',
        subject: 'Happy Birthday, {name}! 🎉',
        message: 'Dear {name},\n\nWishing you a very happy {age}th birthday! May this special day bring you joy, happiness, and wonderful memories.\n\nHave a fantastic celebration!\n\nBest wishes,\n{senderName}',
        variables: ['name', 'age', 'senderName'],
        isActive: true,
      },
      {
        name: 'Wedding Anniversary',
        type: 'anniversary',
        subject: 'Happy Anniversary, {name}! 💕',
        message: 'Dear {name},\n\nWishing you and {partnerName} a wonderful anniversary! May your love continue to grow stronger with each passing year.\n\nCelebrating {years} years of love and happiness!\n\nWith warm wishes,\n{senderName}',
        variables: ['name', 'partnerName', 'years', 'senderName'],
        isActive: true,
      },
      // Add more default templates as needed
    ];
  }

  private async simulateDelay(ms: number = 100): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getCurrentUserId(): string {
    if (!this.currentUserId) {
      throw new Error('No user set. Call setCurrentUser() first.');
    }
    return this.currentUserId;
  }

  // Template Management
  async getTemplates(): Promise<MessageTemplate[]> {
    await this.simulateDelay();

    // Get user templates and default templates
    const userTemplates = persistenceService.getAll<MessageTemplate>('message_templates');
    const defaultTemplates = persistenceService.getAllGlobal<MessageTemplate>('default_templates');

    // Combine and return all templates
    return [...defaultTemplates, ...userTemplates];
  }

  async getTemplateById(id: string): Promise<MessageTemplate | null> {
    await this.simulateDelay();

    // Check user templates first
    let template = await persistenceService.getById<MessageTemplate>('message_templates', id);

    // If not found, check default templates
    if (!template) {
      template = await persistenceService.getByIdGlobal<MessageTemplate>('default_templates', id);
    }

    return template;
  }

  async createTemplate(templateData: Omit<MessageTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<MessageTemplate> {
    await this.simulateDelay();
    return await persistenceService.create<MessageTemplate>('message_templates', templateData);
  }

  async updateTemplate(id: string, updates: Partial<MessageTemplate>): Promise<MessageTemplate> {
    await this.simulateDelay();

    const updatedTemplate = await persistenceService.update<MessageTemplate>('message_templates', id, updates);

    if (!updatedTemplate) {
      throw new Error('Template not found');
    }

    return updatedTemplate;
  }

  async deleteTemplate(id: string): Promise<void> {
    await this.simulateDelay();
    const success = await persistenceService.delete<MessageTemplate>('message_templates', id);
    if (!success) {
      throw new Error('Template not found');
    }
  }

  // Message History
  async getMessageHistory(limit: number = 50): Promise<MessageHistory[]> {
    await this.simulateDelay();
    const allHistory = persistenceService.getAll<MessageHistory>('message_history');

    // Sort by sentAt descending and limit
    return allHistory
      .sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())
      .slice(0, limit);
  }

  async addMessageToHistory(messageData: Omit<MessageHistory, 'id' | 'sentAt'>): Promise<MessageHistory> {
    await this.simulateDelay();

    const messageWithTimestamp = {
      ...messageData,
      sentAt: new Date().toISOString(),
    };

    return await persistenceService.create<MessageHistory>('message_history', messageWithTimestamp);
  }

  // Scheduled Messages
  async getScheduledMessages(): Promise<ScheduledMessage[]> {
    await this.simulateDelay();
    return persistenceService.getAll<ScheduledMessage>('scheduled_messages');
  }

  async scheduleMessage(messageData: Omit<ScheduledMessage, 'id' | 'createdAt'>): Promise<ScheduledMessage> {
    await this.simulateDelay();
    return await persistenceService.create<ScheduledMessage>('scheduled_messages', messageData);
  }

  async updateScheduledMessage(id: string, updates: Partial<ScheduledMessage>): Promise<ScheduledMessage> {
    await this.simulateDelay();

    const updatedMessage = await persistenceService.update<ScheduledMessage>('scheduled_messages', id, updates);

    if (!updatedMessage) {
      throw new Error('Scheduled message not found');
    }

    return updatedMessage;
  }

  async deleteScheduledMessage(id: string): Promise<void> {
    await this.simulateDelay();
    const success = await persistenceService.delete<ScheduledMessage>('scheduled_messages', id);
    if (!success) {
      throw new Error('Scheduled message not found');
    }
  }

  // Settings Management
  async getSettings(): Promise<NotificationSettings> {
    await this.simulateDelay();
    return { ...this.settings };
  }

  async updateSettings(updates: Partial<NotificationSettings>): Promise<NotificationSettings> {
    await this.simulateDelay();
    this.settings = {
      ...this.settings,
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    return { ...this.settings };
  }

  // Admin method to reset templates to defaults
  async resetToDefaultTemplates(): Promise<void> {
    await this.simulateDelay();
    await this.seedDefaultTemplatesIfNeeded();
  }

  // Admin method to clean up storage
  async cleanupStorage(): Promise<{ removedDuplicates: number; totalTemplates: number; }> {
    await this.simulateDelay();
    const templates = persistenceService.getAll<MessageTemplate>('message_templates');

    // For now, just return current count (actual deduplication would be more complex)
    return {
      removedDuplicates: 0,
      totalTemplates: templates.length
    };
  }

  // Utility methods for birthday/anniversary processing
  getLandmarkBirthdays(): LandmarkBirthday[] {
    return [...LANDMARK_BIRTHDAYS];
  }

  isLandmarkBirthday(age: number): LandmarkBirthday | null {
    return LANDMARK_BIRTHDAYS.find(landmark => landmark.age === age) || null;
  }

  calculateAge(birthDate: string): number {
    return differenceInYears(new Date(), parseISO(birthDate));
  }

  // Message sending simulation
  async sendMessage(
    contactId: string,
    templateId: string,
    variables: Record<string, string> = {},
    deliveryMethod: 'email' | 'sms' = 'email'
  ): Promise<void> {
    await this.simulateDelay(1000); // Simulate network delay

    const template = await this.getTemplateById(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    // Replace variables in template
    let subject = template.subject;
    let message = template.message;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      subject = subject.replace(new RegExp(placeholder, 'g'), value);
      message = message.replace(new RegExp(placeholder, 'g'), value);
    });

    // Add to message history
    await this.addMessageToHistory({
      contactId,
      templateId,
      type: template.type,
      subject,
      message,
      status: 'sent',
      deliveryMethod,
    });

    // Simulate sending (in real app, integrate with email/SMS service)
    console.log(`📧 Message sent via ${deliveryMethod}:`, { subject, message });
  }

  // Statistics
  async getStats(): Promise<MessagingStats> {
    await this.simulateDelay();
    const messageHistory = persistenceService.getAll<MessageHistory>('message_history');

    const totalSent = messageHistory.filter(m => m.status === 'sent').length;
    const totalFailed = messageHistory.filter(m => m.status === 'failed').length;
    const birthdayMessages = messageHistory.filter(m => m.type === 'birthday').length;
    const landmarkMessages = messageHistory.filter(m => m.type === 'landmark').length;
    const weeklyMessages = messageHistory.filter(m => m.type === 'weekly_admin').length;

    return {
      totalMessagesSent: totalSent,
      birthdayMessagesSent: birthdayMessages,
      landmarkMessagesSent: landmarkMessages,
      weeklyNotificationsSent: weeklyMessages,
      failedMessages: totalFailed,
      successRate: totalSent + totalFailed > 0 ? (totalSent / (totalSent + totalFailed)) * 100 : 0,
      lastWeekStats: {
        sent: totalSent, // Would need date filtering for actual last week
        failed: totalFailed,
      },
    };
  }
}

// Export singleton instance
export const messagingService = new MessagingService();
export default messagingService;
